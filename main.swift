import Cocoa

class AppDelegate: NSObject, NSApplicationDelegate {
    
    var window: NSWindow!
    
    func applicationDidFinishLaunching(_ aNotification: Notification) {
        
        // 获取主屏幕尺寸
        let screenSize = NSScreen.main?.frame.size ?? CGSize(width: 1200, height: 800)
        
        // 创建全屏窗口
        let windowRect = NSRect(x: 0, y: 0, width: screenSize.width, height: screenSize.height)
        
        window = NSWindow(
            contentRect: windowRect,
            styleMask: [.borderless, .fullSizeContentView], // 无边框全屏
            backing: .buffered,
            defer: false
        )
        
        window.title = "太阳系模型"
        window.isOpaque = true
        window.backgroundColor = NSColor.white
        window.makeKeyAndOrderFront(nil)
        
        // 进入全屏模式
        window.toggleFullScreen(nil)
        
        // 创建视图控制器
        let viewController = ViewController()
        window.contentViewController = viewController
        
        // 让窗口成为主窗口
        NSApp.activate(ignoringOtherApps: true)
    }
    
    func applicationWillTerminate(_ aNotification: Notification) {
        // 清理资源
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}

// 应用程序入口点
let app = NSApplication.shared
let delegate = AppDelegate()
app.delegate = delegate
app.run()